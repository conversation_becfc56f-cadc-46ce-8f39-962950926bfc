import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:learning2/services/display_service.dart';

/// Widget for monitoring and displaying performance metrics
class PerformanceMonitor extends StatefulWidget {
  final Widget child;
  final bool showOverlay;
  final bool enableLogging;
  
  const PerformanceMonitor({
    super.key,
    required this.child,
    this.showOverlay = false,
    this.enableLogging = true,
  });
  
  @override
  State<PerformanceMonitor> createState() => _PerformanceMonitorState();
}

class _PerformanceMonitorState extends State<PerformanceMonitor> {
  double _currentFps = 0.0;
  double _averageFps = 0.0;
  int _droppedFrames = 0;
  int _totalFrames = 0;
  double _maxFrameTime = 0.0;
  double _currentFrameTime = 0.0;
  
  final List<double> _frameTimes = [];
  final int _maxFrameHistory = 60; // Keep last 60 frames
  
  @override
  void initState() {
    super.initState();
    _startMonitoring();
  }
  
  void _startMonitoring() {
    SchedulerBinding.instance.addTimingsCallback(_onFrameTimings);
  }
  
  void _onFrameTimings(List<FrameTiming> timings) {
    if (!mounted) return;
    
    for (final timing in timings) {
      final frameTime = timing.totalSpan.inMicroseconds / 1000.0; // Convert to milliseconds
      _currentFrameTime = frameTime;
      
      // Update frame times history
      _frameTimes.add(frameTime);
      if (_frameTimes.length > _maxFrameHistory) {
        _frameTimes.removeAt(0);
      }
      
      // Calculate FPS
      _currentFps = 1000.0 / frameTime;
      
      // Calculate average FPS
      if (_frameTimes.isNotEmpty) {
        final averageFrameTime = _frameTimes.reduce((a, b) => a + b) / _frameTimes.length;
        _averageFps = 1000.0 / averageFrameTime;
      }
      
      // Track dropped frames (frames taking longer than 16.67ms for 60fps)
      final targetFrameTime = 1000.0 / (DisplayService.instance.currentRefreshRate ?? 60.0);
      if (frameTime > targetFrameTime * 1.5) {
        _droppedFrames++;
        
        if (widget.enableLogging) {
          print('Dropped frame: ${frameTime.toStringAsFixed(2)}ms (target: ${targetFrameTime.toStringAsFixed(2)}ms)');
        }
      }
      
      _totalFrames++;
      
      // Track max frame time
      if (frameTime > _maxFrameTime) {
        _maxFrameTime = frameTime;
      }
      
      // Update UI if overlay is enabled
      if (widget.showOverlay && mounted) {
        setState(() {});
      }
    }
  }
  
  @override
  void dispose() {
    SchedulerBinding.instance.removeTimingsCallback(_onFrameTimings);
    super.dispose();
  }
  
  Widget _buildPerformanceOverlay() {
    final displayService = DisplayService.instance;
    final refreshRate = displayService.currentRefreshRate ?? 60.0;
    final category = displayService.getRefreshRateCategory();
    
    return Positioned(
      top: 50,
      right: 10,
      child: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.black.withOpacity(0.8),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Performance Monitor',
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 12,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              'Refresh Rate: ${refreshRate.toStringAsFixed(1)}Hz',
              style: const TextStyle(color: Colors.white, fontSize: 10),
            ),
            Text(
              'Category: ${category.name}',
              style: const TextStyle(color: Colors.white, fontSize: 10),
            ),
            Text(
              'Current FPS: ${_currentFps.toStringAsFixed(1)}',
              style: TextStyle(
                color: _currentFps >= refreshRate * 0.9 ? Colors.green : Colors.red,
                fontSize: 10,
              ),
            ),
            Text(
              'Average FPS: ${_averageFps.toStringAsFixed(1)}',
              style: const TextStyle(color: Colors.white, fontSize: 10),
            ),
            Text(
              'Frame Time: ${_currentFrameTime.toStringAsFixed(2)}ms',
              style: const TextStyle(color: Colors.white, fontSize: 10),
            ),
            Text(
              'Max Frame Time: ${_maxFrameTime.toStringAsFixed(2)}ms',
              style: const TextStyle(color: Colors.white, fontSize: 10),
            ),
            Text(
              'Dropped Frames: $_droppedFrames/$_totalFrames',
              style: TextStyle(
                color: _droppedFrames == 0 ? Colors.green : Colors.orange,
                fontSize: 10,
              ),
            ),
            Text(
              'Drop Rate: ${(_droppedFrames / (_totalFrames > 0 ? _totalFrames : 1) * 100).toStringAsFixed(1)}%',
              style: const TextStyle(color: Colors.white, fontSize: 10),
            ),
          ],
        ),
      ),
    );
  }
  
  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        widget.child,
        if (widget.showOverlay) _buildPerformanceOverlay(),
      ],
    );
  }
  
  /// Get current performance metrics
  PerformanceMetrics getMetrics() {
    return PerformanceMetrics(
      currentFps: _currentFps,
      averageFps: _averageFps,
      droppedFrames: _droppedFrames,
      totalFrames: _totalFrames,
      maxFrameTime: _maxFrameTime,
      currentFrameTime: _currentFrameTime,
      dropRate: _droppedFrames / (_totalFrames > 0 ? _totalFrames : 1),
    );
  }
  
  /// Reset performance counters
  void resetMetrics() {
    setState(() {
      _droppedFrames = 0;
      _totalFrames = 0;
      _maxFrameTime = 0.0;
      _frameTimes.clear();
    });
  }
}

/// Performance metrics data class
class PerformanceMetrics {
  final double currentFps;
  final double averageFps;
  final int droppedFrames;
  final int totalFrames;
  final double maxFrameTime;
  final double currentFrameTime;
  final double dropRate;
  
  const PerformanceMetrics({
    required this.currentFps,
    required this.averageFps,
    required this.droppedFrames,
    required this.totalFrames,
    required this.maxFrameTime,
    required this.currentFrameTime,
    required this.dropRate,
  });
  
  /// Check if performance is good (low drop rate, high FPS)
  bool get isPerformanceGood {
    final targetFps = DisplayService.instance.currentRefreshRate ?? 60.0;
    return averageFps >= targetFps * 0.9 && dropRate < 0.05; // Less than 5% drop rate
  }
  
  /// Get performance grade (A, B, C, D, F)
  String get performanceGrade {
    if (dropRate < 0.01 && averageFps >= (DisplayService.instance.currentRefreshRate ?? 60.0) * 0.95) {
      return 'A';
    } else if (dropRate < 0.03 && averageFps >= (DisplayService.instance.currentRefreshRate ?? 60.0) * 0.85) {
      return 'B';
    } else if (dropRate < 0.05 && averageFps >= (DisplayService.instance.currentRefreshRate ?? 60.0) * 0.75) {
      return 'C';
    } else if (dropRate < 0.10 && averageFps >= (DisplayService.instance.currentRefreshRate ?? 60.0) * 0.60) {
      return 'D';
    } else {
      return 'F';
    }
  }
  
  @override
  String toString() {
    return 'PerformanceMetrics(fps: ${averageFps.toStringAsFixed(1)}, '
           'dropRate: ${(dropRate * 100).toStringAsFixed(1)}%, '
           'grade: $performanceGrade)';
  }
}
